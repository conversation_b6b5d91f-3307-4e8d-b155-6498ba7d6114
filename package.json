{"name": "<PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "dependencies": {"@ant-design/icons": "^4.7.0", "@datadog/browser-logs": "^5.29.1", "@datadog/browser-rum": "^5.32.0", "@excalidraw/excalidraw": "^0.17.6", "@jitsi/react-sdk": "^1.3.0", "@livekit/components-core": "^0.10.2", "@livekit/components-react": "^2.3.1", "@livekit/components-styles": "^1.0.12", "@livekit/react-core": "^1.1.0", "@livekit/track-processors": "^0.5.7", "@loadable/component": "^5.15.2", "@reduxjs/toolkit": "^1.8.2", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "@testrtc/watchrtc-sdk": "^1.40.1", "antd": "^4.23.4", "antd-img-crop": "^4.12.2", "apexcharts": "^3.49.2", "axios": "^0.27.2", "bootstrap": "^5.1.3", "browser-fs-access": "^0.35.0", "chart.js": "^3.9.1", "clsx": "^2.1.1", "crypto-js": "^4.1.1", "emoji-picker-react": "^4.12.2", "formik": "^2.2.9", "html-react-parser": "^3.0.4", "html2canvas": "^1.4.1", "i18next": "^21.8.5", "i18next-browser-languagedetector": "^6.1.4", "is-electron": "^2.2.2", "jquery": "^3.6.1", "jspdf": "^2.5.1", "livekit-client": "https://github.com/sumeet-dank/client-sdk-js.git", "lodash": "^4.17.21", "lottie-react": "^2.4.0", "mic-recorder-to-mp3": "^2.2.2", "moment": "^2.29.3", "moment-timezone": "^0.5.34", "perfect-freehand": "^1.2.2", "prop-types": "^15.8.1", "qs": "^6.13.0", "react": "^18.2.0", "react-apexcharts": "^1.4.1", "react-bootstrap": "^2.4.0", "react-bootstrap-table-next": "^4.0.3", "react-chartjs-2": "^4.3.1", "react-csv": "^2.2.2", "react-dom": "^18.3.1", "react-flags-select": "^2.2.3", "react-google-recaptcha-v3": "^1.10.1", "react-helmet-async": "^1.3.0", "react-i18next": "^11.16.9", "react-icons": "^5.2.1", "react-joyride": "^2.9.3", "react-pdf": "^10.0.1", "react-phone-input-2": "^2.15.1", "react-player": "^2.16.0", "react-redux": "^8.0.2", "react-resizable-panels": "^2.1.8", "react-ripples": "^2.2.1", "react-router-dom": "^6.3.0", "react-rte": "^0.16.5", "react-scripts": "5.0.1", "react-scroll": "^1.9.0", "react-select": "^5.4.0", "react-share": "^4.4.1", "react-slick": "^0.29.0", "react-spinners": "^0.13.4", "react-summernote": "^2.0.2", "react-use-pip": "^1.5.0", "redux": "^4.2.0", "redux-persist": "^6.0.0", "redux-persist-transform-encrypt": "^3.0.1", "redux-state-sync": "^3.1.4", "resolve-url-loader": "^5.0.0", "sass": "^1.55.0", "simplebar-react": "^2.4.3", "slick-carousel": "^1.8.1", "socket.io-client": "^4.8.1", "sweetalert2": "^11.4.33", "toastr": "^2.1.4", "web-vitals": "^2.1.4", "yup": "^0.32.11"}, "scripts": {"start": "react-scripts start", "docker": "WATCHPACK_POLLING=true react-scripts start", "build": "CI=false && react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint ./", "lint-fix": "eslint ./ --fix", "format": "prettier --write \"**/*.{js,jsx,json,md}\""}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"eslint": "^8.16.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.30.0", "eslint-plugin-react-hooks": "^4.5.0", "husky": "^8.0.1", "lint-staged": "^13.0.0", "prettier": "^2.6.2", "pretty-quick": "^3.1.3"}, "lint-staged": {"*.js": "eslint"}, "husky": {"hooks": {"pre-commit": "lint-staged && pretty-quick --staged"}}}