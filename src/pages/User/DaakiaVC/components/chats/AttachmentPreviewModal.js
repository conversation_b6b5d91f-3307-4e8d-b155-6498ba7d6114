import React, { useState } from 'react';
import { Modal } from 'antd';
import { MdFileDownload, MdOutlineZoomIn, MdOutlineZoomOut, MdZoomInMap, MdNavigateBefore, MdNavigateNext } from 'react-icons/md';
import { Document, Page, pdfjs } from 'react-pdf';

// Set up PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

/**
 * Reusable Attachment Preview Modal Component
 * Supports both PDF and Image previews with zoom functionality
 */
export function AttachmentPreviewModal({
  isOpen,
  onClose,
  entry,
  canDownloadChatAttachment = true,
}) {
  const [scale, setScale] = useState(1);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [startPos, setStartPos] = useState({ x: 0, y: 0 });

  // PDF-specific state
  const [numPages, setNumPages] = useState(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [pdfScale, setPdfScale] = useState(1.0);

  // Image zoom controls
  const zoomIn = () => setScale((prev) => Math.min(prev + 0.2, 3)); // Max zoom level: 3x
  const zoomOut = () => setScale((prev) => Math.max(prev - 0.2, 1)); // Min zoom level: 1x
  const resetZoom = () => setScale(1); // Reset zoom to default

  // PDF controls
  const pdfZoomIn = () => setPdfScale((prev) => Math.min(prev + 0.2, 3));
  const pdfZoomOut = () => setPdfScale((prev) => Math.max(prev - 0.2, 0.5));
  const pdfResetZoom = () => setPdfScale(1);
  const goToPrevPage = () => setPageNumber((prev) => Math.max(prev - 1, 1));
  const goToNextPage = () => setPageNumber((prev) => Math.min(prev + 1, numPages || 1));

  const onDocumentLoadSuccess = ({ numPages }) => {
    setNumPages(numPages);
    setPageNumber(1);
  };

  // Drag controls for images
  const handleMouseDown = (e) => {
    setIsDragging(true);
    setStartPos({ x: e.clientX - position.x, y: e.clientY - position.y });
  };

  const handleMouseMove = (e) => {
    if (isDragging) {
      setPosition({ x: e.clientX - startPos.x, y: e.clientY - startPos.y });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleDownload = () => {
    window.open(entry.message.split(" ")[0], "_blank");
  };

  const handleClose = () => {
    setPosition({ x: 0, y: 0 });
    setScale(1);
    setIsDragging(false);
    onClose();
  };

  if (!entry || !isOpen) return null;

  const fileUrl = entry.message.split(" ")[0];
  const fileName = entry.message.split("-file-").pop();
  const isPDF = /pdf/.test(entry.message);
  const isImage = /jpeg|jpg|png|gif/.test(entry.message);

  return (
    <Modal
      open={isOpen}
      onCancel={handleClose}
      footer={null}
      className="lk-attatchment-preview"
      maskStyle={{
        background: "rgba(0, 0, 0, 0.7)",
        backdropFilter: "blur(4px)",
      }}

      width="50%"
      centered
    >
      {/* Download Button */}
      {canDownloadChatAttachment && (
        <MdFileDownload
          onClick={handleDownload}
          className="attatchment-download"
        />
      )}
      
      {/* File Name */}
      <span className="attatchment-name">
        {fileName}
      </span>

      {/* PDF Preview */}
      {isPDF && (
        <div style={{ position: "relative", textAlign: "center" }}>
          {/* PDF Controls */}
          <div style={{
            position: "absolute",
            top: "10px",
            right: "10px",
            zIndex: 10,
            display: "flex",
            flexDirection: "column",
            gap: "5px"
          }}>
            <MdOutlineZoomIn
              onClick={pdfZoomIn}
              className="attatchment-zoom-in"
              style={{
                cursor: "pointer",
                background: "rgba(0,0,0,0.7)",
                color: "white",
                padding: "5px",
                borderRadius: "3px"
              }}
            />
            <MdOutlineZoomOut
              onClick={pdfZoomOut}
              className="attatchment-zoom-out"
              style={{
                cursor: "pointer",
                background: "rgba(0,0,0,0.7)",
                color: "white",
                padding: "5px",
                borderRadius: "3px"
              }}
            />
            <MdZoomInMap
              onClick={pdfResetZoom}
              className="attatchment-zoom-reset"
              style={{
                cursor: "pointer",
                background: "rgba(0,0,0,0.7)",
                color: "white",
                padding: "5px",
                borderRadius: "3px"
              }}
            />
          </div>

          {/* Page Navigation */}
          {numPages > 1 && (
            <div style={{
              position: "absolute",
              bottom: "10px",
              left: "50%",
              transform: "translateX(-50%)",
              zIndex: 10,
              display: "flex",
              alignItems: "center",
              gap: "10px",
              background: "rgba(0,0,0,0.7)",
              color: "white",
              padding: "5px 10px",
              borderRadius: "5px"
            }}>
              <MdNavigateBefore
                onClick={goToPrevPage}
                style={{ cursor: pageNumber <= 1 ? "not-allowed" : "pointer", opacity: pageNumber <= 1 ? 0.5 : 1 }}
              />
              <span>{pageNumber} / {numPages}</span>
              <MdNavigateNext
                onClick={goToNextPage}
                style={{ cursor: pageNumber >= numPages ? "not-allowed" : "pointer", opacity: pageNumber >= numPages ? 0.5 : 1 }}
              />
            </div>
          )}

          {/* React-PDF Document */}
          <Document
            file={fileUrl}
            onLoadSuccess={onDocumentLoadSuccess}
            loading={<div>Loading PDF...</div>}
            error={<div>Failed to load PDF</div>}
          >
            <Page
              pageNumber={pageNumber}
              scale={pdfScale}
              renderTextLayer={false}
              renderAnnotationLayer={false}
            />
          </Document>
        </div>
      )}

      {/* Image Preview with Zoom */}
      {isImage && (
        <div
          className="lk-chat-image-preview-container"
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
          style={{
            cursor: isDragging ? "grabbing" : "grab",
            overflow: "hidden",
            position: "relative",
            height: "400px",
            background: "transparent",
          }}
        >
          <img
            src={fileUrl}
            alt="Attachment Preview"
            style={{
              width: "100%",
              transform: `translate(${position.x}px, ${position.y}px) scale(${scale})`,
              transition: isDragging ? "none" : "transform 0.2s ease-in-out",
              cursor: isDragging ? "grabbing" : "grab",
            }}
            onMouseDown={handleMouseDown}
            draggable={false}
          />
          
          {/* Zoom Controls */}
          <MdOutlineZoomIn
            onClick={zoomIn}
            className="attatchment-zoom-in"
          />
          <MdOutlineZoomOut
            onClick={zoomOut}
            className="attatchment-zoom-out"
          />
          <MdZoomInMap
            onClick={resetZoom}
            className="attatchment-zoom-reset"
          />
        </div>
      )}
    </Modal>
  );
}

export default AttachmentPreviewModal;
